name: ci
on:
  push:
    branches:
      - master 
      - main
permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email github-actions[bot]@users.noreply.github.com

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
            enable-cache: true
            cache-dependency-glob: "uv.lock"

      - uses: actions/setup-python@v5
        with:
          python-version-file: "pyproject.toml"

      - name: Install the project
        run: uv sync --all-extras --dev

      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV 
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache 
          restore-keys: |
            mkdocs-material-

      - run: uv run mkdocs gh-deploy --force
